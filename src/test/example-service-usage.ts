/**
 * Example file demonstrating how to use the service layer interfaces for testing
 * This shows how the service interfaces make mocking and testing much easier
 */

import { IApiService, IDatabaseService, INotificationService } from '../services/interfaces';
import { getServices } from '../services/registry';
import { CursorStats, UsageInfo } from '../interfaces/types';

/**
 * Example mock implementation of the API service for testing
 */
class MockApiService implements IApiService {
  async fetchCursorStats(_token: string): Promise<CursorStats> {
    // Return mock data for testing
    return {
      currentMonth: {
        month: 12,
        year: 2024,
        usageBasedPricing: {
          items: [],
          hasUnpaidMidMonthInvoice: false,
          midMonthPayment: 0,
        },
      },
      lastMonth: {
        month: 11,
        year: 2024,
        usageBasedPricing: {
          items: [],
          hasUnpaidMidMonthInvoice: false,
          midMonthPayment: 0,
        },
      },
      premiumRequests: {
        current: 50,
        limit: 500,
        startOfMonth: '1 December',
      },
      isTeamSpendData: false,
    };
  }

  async checkUsageBasedStatus(
    _token: string,
    _teamId?: number,
  ): Promise<{ isEnabled: boolean; limit?: number }> {
    return { isEnabled: true, limit: 20 };
  }

  async getCurrentUsageLimit(_token: string): Promise<any> {
    return { limit: 20 };
  }

  async setUsageLimit(
    _token: string,
    _hardLimit: number,
    _noUsageBasedAllowed: boolean,
  ): Promise<void> {
    // Mock implementation - do nothing
  }

  async getStripeSessionUrl(_token: string): Promise<string> {
    return 'https://mock-stripe-url.com';
  }
}

/**
 * Example mock implementation of the database service for testing
 */
class MockDatabaseService implements IDatabaseService {
  async getCursorTokenFromDB(): Promise<string | undefined> {
    return 'mock-token-123';
  }

  getCursorDBPath(): string {
    return '/mock/path/to/database.db';
  }
}

/**
 * Example mock implementation of the notification service for testing
 */
class MockNotificationService implements INotificationService {
  private notificationsCalled: string[] = [];

  async checkAndNotifyUsage(_usageInfo: UsageInfo, _token: string): Promise<void> {
    this.notificationsCalled.push('usage');
  }

  async checkAndNotifySpending(_totalSpent: number): Promise<void> {
    this.notificationsCalled.push('spending');
  }

  async checkAndNotifyUnpaidInvoice(_token: string): Promise<void> {
    this.notificationsCalled.push('unpaid-invoice');
  }

  resetNotifications(): void {
    this.notificationsCalled = [];
  }

  // Test helper method
  getNotificationsCalled(): string[] {
    return [...this.notificationsCalled];
  }
}

/**
 * Example function that uses services through the registry
 * This demonstrates how business logic can be tested by mocking services
 */
export async function exampleBusinessLogic(): Promise<{ success: boolean; data?: any }> {
  const services = getServices();

  try {
    // Get token from database
    const token = await services.databaseService.getCursorTokenFromDB();
    if (!token) {
      return { success: false };
    }

    // Fetch stats from API
    const stats = await services.apiService.fetchCursorStats(token);

    // Check if we need to notify about usage
    const usageInfo: UsageInfo = {
      percentage: (stats.premiumRequests.current / stats.premiumRequests.limit) * 100,
      type: 'premium',
      limit: stats.premiumRequests.limit,
    };

    if (usageInfo.percentage > 80) {
      await services.notificationService.checkAndNotifyUsage(usageInfo, token);
    }

    return { success: true, data: stats };
  } catch (error) {
    services.loggingService.log('Error in business logic', error, true);
    return { success: false };
  }
}

/**
 * Example test function showing how to mock services for testing
 */
export async function exampleTest(): Promise<boolean> {
  const services = getServices();

  // Save original services
  const originalApiService = services.apiService;
  const originalDatabaseService = services.databaseService;
  const originalNotificationService = services.notificationService;

  try {
    // Replace with mock implementations
    const mockNotificationService = new MockNotificationService();
    services.setApiService(new MockApiService());
    services.setDatabaseService(new MockDatabaseService());
    services.setNotificationService(mockNotificationService);

    // Run the business logic with mocked services
    const result = await exampleBusinessLogic();

    // Verify the results
    const success =
      result.success === true &&
      result.data?.premiumRequests?.current === 50 &&
      mockNotificationService.getNotificationsCalled().length === 0; // Usage is 10%, below 80%

    return success;
  } finally {
    // Restore original services
    services.setApiService(originalApiService);
    services.setDatabaseService(originalDatabaseService);
    services.setNotificationService(originalNotificationService);
  }
}

/**
 * This example demonstrates the key benefits of the service layer:
 *
 * 1. **Testability**: Services can be easily mocked for unit testing
 * 2. **Separation of Concerns**: Business logic is separated from implementation details
 * 3. **Dependency Injection**: Services can be swapped out without changing business logic
 * 4. **Interface Contracts**: Clear contracts define what each service should do
 * 5. **Maintainability**: Changes to service implementations don't affect business logic
 */
