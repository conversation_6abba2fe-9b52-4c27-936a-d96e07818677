/**
 * Service layer interfaces for the Cursor Stats extension
 * These interfaces define the contracts for all services, making them easily mockable for testing
 */

import * as vscode from 'vscode';
import { CursorStats, UsageLimitResponse, UsageInfo } from '../interfaces/types';

/**
 * Interface for API service operations
 * Handles all external API calls to Cursor's backend services
 */
export interface IApiService {
  /**
   * Fetch comprehensive cursor usage statistics
   * @param token - Authentication token
   * @returns Promise resolving to cursor stats data
   */
  fetchCursorStats(token: string): Promise<CursorStats>;

  /**
   * Check if usage-based pricing is enabled for the user/team
   * @param token - Authentication token
   * @param teamId - Optional team ID for team-specific checks
   * @returns Promise resolving to usage-based status
   */
  checkUsageBasedStatus(
    token: string,
    teamId?: number,
  ): Promise<{ isEnabled: boolean; limit?: number }>;

  /**
   * Get current usage limit for the user
   * @param token - Authentication token
   * @returns Promise resolving to usage limit response
   */
  getCurrentUsageLimit(token: string): Promise<UsageLimitResponse>;

  /**
   * Set usage limit for the user
   * @param token - Authentication token
   * @param hardLimit - New hard limit to set
   * @param noUsageBasedAllowed - Whether usage-based pricing is allowed
   * @returns Promise resolving when limit is set
   */
  setUsageLimit(token: string, hardLimit: number, noUsageBasedAllowed: boolean): Promise<void>;

  /**
   * Get Stripe session URL for billing management
   * @param token - Authentication token
   * @returns Promise resolving to Stripe session URL
   */
  getStripeSessionUrl(token: string): Promise<string>;
}

/**
 * Interface for database service operations
 * Handles SQLite database interactions for token retrieval and path management
 */
export interface IDatabaseService {
  /**
   * Retrieve Cursor authentication token from local database
   * @returns Promise resolving to token string or undefined if not found
   */
  getCursorTokenFromDB(): Promise<string | undefined>;

  /**
   * Get the path to the Cursor database file
   * @returns Absolute path to the database file
   */
  getCursorDBPath(): string;
}

/**
 * Interface for notification service operations
 * Handles user notifications for usage thresholds and billing alerts
 */
export interface INotificationService {
  /**
   * Check usage levels and notify user if thresholds are exceeded
   * @param usageInfo - Current usage information
   * @param token - Authentication token for additional operations
   * @returns Promise resolving when notification check is complete
   */
  checkAndNotifyUsage(usageInfo: UsageInfo, token: string): Promise<void>;

  /**
   * Check spending levels and notify user if thresholds are exceeded
   * @param totalSpent - Total amount spent
   * @returns Promise resolving when spending check is complete
   */
  checkAndNotifySpending(totalSpent: number): Promise<void>;

  /**
   * Check for unpaid invoices and notify user
   * @param token - Authentication token for billing operations
   * @returns Promise resolving when invoice check is complete
   */
  checkAndNotifyUnpaidInvoice(token: string): Promise<void>;

  /**
   * Reset all notification tracking state
   * Called when extension is activated or needs to reset notification state
   */
  resetNotifications(): void;
}

/**
 * Interface for team service operations
 * Handles team membership checks and team-related data retrieval
 */
export interface ITeamService {
  /**
   * Check if user is a team member and get team information
   * @param token - Authentication token
   * @param context - VSCode extension context for caching
   * @returns Promise resolving to team membership information
   */
  checkTeamMembership(
    token: string,
    context: vscode.ExtensionContext,
  ): Promise<{ isTeamMember: boolean; teamId?: number; userId?: number; startOfMonth: string }>;

  /**
   * Get team spending data
   * @param token - Authentication token
   * @param teamId - Team ID
   * @returns Promise resolving to team spending response
   */
  getTeamSpend(token: string, teamId: number): Promise<any>;

  /**
   * Extract user-specific spending from team data
   * @param teamSpend - Team spending response data
   * @param userId - User ID to extract data for
   * @returns User spending data object
   */
  extractUserSpend(teamSpend: any, userId: number): any;
}

/**
 * Interface for GitHub service operations
 * Handles GitHub release checking and update notifications
 */
export interface IGitHubService {
  /**
   * Check for new GitHub releases
   * @returns Promise resolving to release information or null if no updates
   */
  checkGitHubRelease(): Promise<{
    hasUpdate: boolean;
    currentVersion: string;
    latestVersion: string;
    isPrerelease: boolean;
    releaseUrl: string;
    releaseNotes: string;
    releaseName: string;
    zipballUrl: string;
    tarballUrl: string;
    assets: Array<{ name: string; downloadUrl: string }>;
  } | null>;

  /**
   * Check for updates and handle notifications
   * @param lastReleaseCheck - Timestamp of last release check
   * @param releaseCheckInterval - Interval between release checks
   * @param specificVersion - Optional specific version to check
   * @returns Promise resolving when update check is complete
   */
  checkForUpdates(
    lastReleaseCheck: number,
    releaseCheckInterval: number,
    specificVersion?: string,
  ): Promise<void>;
}

/**
 * Interface for status bar service operations
 * Handles status bar creation, updates, and tooltip management
 */
export interface IStatusBarService {
  /**
   * Create and configure the status bar item
   * @returns Status bar item
   */
  createStatusBarItem(): vscode.StatusBarItem;

  /**
   * Create markdown tooltip for status bar
   * @param lines - Array of tooltip lines
   * @param isError - Whether this is an error tooltip
   * @param allLines - All available lines for the tooltip
   * @returns Promise resolving to formatted markdown string
   */
  createMarkdownTooltip(
    lines: string[],
    isError?: boolean,
    allLines?: string[],
  ): Promise<vscode.MarkdownString>;

  /**
   * Format a single tooltip line
   * @param text - Text to format
   * @param maxWidth - Maximum width for formatting
   * @returns Formatted line string
   */
  formatTooltipLine(text: string, maxWidth?: number): string;

  /**
   * Get status bar color based on usage percentage
   * @param percentage - Usage percentage (0-100)
   * @returns Color string or ThemeColor for status bar
   */
  getStatusBarColor(percentage: number): vscode.ThemeColor | string;
}

/**
 * Interface for logging service operations
 * Handles application logging with different levels and output channels
 */
export interface ILoggingService {
  /**
   * Initialize the logging system
   * @param context - VSCode extension context
   */
  initializeLogging(context: vscode.ExtensionContext): void;

  /**
   * Log a message with optional data and error flag
   * @param message - Message to log
   * @param data - Optional additional data to log
   * @param error - Whether this is an error message
   */
  log(message: string, data?: any, error?: boolean): void;

  /**
   * Get the complete log history for reporting
   * @returns Array of log messages
   */
  getLogHistory(): string[];
}
