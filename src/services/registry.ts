/**
 * Service registry for dependency injection and service management
 * Provides centralized access to all service implementations
 */

import {
  IApiService,
  IDatabaseService,
  INotificationService,
  ITeamService,
  IGitHubService,
  IStatusBarService,
  ILoggingService,
} from './interfaces';

import { apiService } from './api';
import { databaseService } from './database';
import { notificationService } from '../handlers/notifications';
import { teamService } from './team';
import { gitHubService } from './github';
import { statusBarService } from '../handlers/statusBar';
import { loggingService } from '../utils/logger';

/**
 * Service registry class that manages all service instances
 * This provides a centralized way to access services and makes testing easier
 */
export class ServiceRegistry {
  private static instance: ServiceRegistry;

  private _apiService: IApiService;
  private _databaseService: IDatabaseService;
  private _notificationService: INotificationService;
  private _teamService: ITeamService;
  private _gitHubService: IGitHubService;
  private _statusBarService: IStatusBarService;
  private _loggingService: ILoggingService;

  private constructor() {
    // Initialize with default implementations
    this._apiService = apiService;
    this._databaseService = databaseService;
    this._notificationService = notificationService;
    this._teamService = teamService;
    this._gitHubService = gitHubService;
    this._statusBarService = statusBarService;
    this._loggingService = loggingService;
  }

  /**
   * Get the singleton instance of the service registry
   */
  public static getInstance(): ServiceRegistry {
    if (!ServiceRegistry.instance) {
      ServiceRegistry.instance = new ServiceRegistry();
    }
    return ServiceRegistry.instance;
  }

  /**
   * Get the API service instance
   */
  public get apiService(): IApiService {
    return this._apiService;
  }

  /**
   * Get the database service instance
   */
  public get databaseService(): IDatabaseService {
    return this._databaseService;
  }

  /**
   * Get the notification service instance
   */
  public get notificationService(): INotificationService {
    return this._notificationService;
  }

  /**
   * Get the team service instance
   */
  public get teamService(): ITeamService {
    return this._teamService;
  }

  /**
   * Get the GitHub service instance
   */
  public get gitHubService(): IGitHubService {
    return this._gitHubService;
  }

  /**
   * Get the status bar service instance
   */
  public get statusBarService(): IStatusBarService {
    return this._statusBarService;
  }

  /**
   * Get the logging service instance
   */
  public get loggingService(): ILoggingService {
    return this._loggingService;
  }

  /**
   * Replace the API service implementation (useful for testing)
   */
  public setApiService(service: IApiService): void {
    this._apiService = service;
  }

  /**
   * Replace the database service implementation (useful for testing)
   */
  public setDatabaseService(service: IDatabaseService): void {
    this._databaseService = service;
  }

  /**
   * Replace the notification service implementation (useful for testing)
   */
  public setNotificationService(service: INotificationService): void {
    this._notificationService = service;
  }

  /**
   * Replace the team service implementation (useful for testing)
   */
  public setTeamService(service: ITeamService): void {
    this._teamService = service;
  }

  /**
   * Replace the GitHub service implementation (useful for testing)
   */
  public setGitHubService(service: IGitHubService): void {
    this._gitHubService = service;
  }

  /**
   * Replace the status bar service implementation (useful for testing)
   */
  public setStatusBarService(service: IStatusBarService): void {
    this._statusBarService = service;
  }

  /**
   * Replace the logging service implementation (useful for testing)
   */
  public setLoggingService(service: ILoggingService): void {
    this._loggingService = service;
  }

  /**
   * Reset all services to their default implementations
   * Useful for cleaning up after tests
   */
  public resetToDefaults(): void {
    this._apiService = apiService;
    this._databaseService = databaseService;
    this._notificationService = notificationService;
    this._teamService = teamService;
    this._gitHubService = gitHubService;
    this._statusBarService = statusBarService;
    this._loggingService = loggingService;
  }
}

/**
 * Convenience function to get the service registry instance
 */
export function getServices(): ServiceRegistry {
  return ServiceRegistry.getInstance();
}
